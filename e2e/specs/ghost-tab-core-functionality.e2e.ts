import {
  setupE2ETestHooks,
  executeCommand,
  expectNotice,
  expectPostFile
} from '../helpers/shared-context';
import { completeModalInteraction } from '../helpers/modal-helpers';
import {
  openGhostTab,
  clickSyncButton,
  clickPublishButton,
  clickBrowsePostsButton,
  waitForGhostTabStatus,
  getGhostTabSyncStatus,
  isGhostTabVisible,
  syncPost
} from '../helpers/ghost-tab-helpers';
import { setupTestFailureHandler } from '../helpers/test-failure-handler';
import { TestPostManager } from '../helpers/ghost-api-helpers';

import { test, expect, describe, beforeEach, afterEach } from 'vitest';

// Setup screenshot capture on test failures
setupTestFailureHandler();

describe("Ghost Tab: Core Functionality", () => {
  const context = setupE2ETestHooks();
  let postManager: TestPostManager;

  beforeEach(async () => {
    postManager = new TestPostManager();
  });

  afterEach(async () => {
    if (postManager) {
      await postManager.cleanup();
    }
  });

  describe("Ghost Tab UI States", () => {
    test("should show 'No file selected' when no file is open", async () => {
      await openGhostTab(context);

      // Wait for the no-file state
      await waitForGhostTabStatus(context.page, 'no-file');

      // Verify the message is displayed
      const noFileMessage = await context.page.textContent('.ghost-sync-no-file');
      expect(noFileMessage).toBe('No file selected');
    });

    test("should show 'File must be in articles directory' for files outside articles dir", async () => {
      // Create a file outside the articles directory
      await context.page.evaluate(() => {
        const app = (window as any).app;
        return app.vault.create('test-outside-articles.md', '# Test Content');
      });

      // Open the file
      await context.page.evaluate(() => {
        const app = (window as any).app;
        const file = app.vault.getAbstractFileByPath('test-outside-articles.md');
        if (file) {
          app.workspace.getLeaf().openFile(file);
        }
      });

      await openGhostTab(context);

      // Wait for the not-article state
      await waitForGhostTabStatus(context.page, 'not-article');

      // Verify the message is displayed
      const notArticleMessage = await context.page.textContent('.ghost-sync-not-article');
      expect(notArticleMessage).toContain('File must be in');
      expect(notArticleMessage).toContain('directory');
    });

    test("should show new post state for unsynced articles", async () => {
      const testTitle = "New Post State Test";

      // Create a new post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'new-post');

      // Verify new post UI elements
      const newPostIcon = await context.page.textContent('.ghost-sync-new-post-icon');
      expect(newPostIcon).toBe('📝');

      const newPostHeading = await context.page.textContent('.ghost-sync-new-post h3');
      expect(newPostHeading).toBe('New Post');

      const syncStatus = await getGhostTabSyncStatus(context.page);
      expect(syncStatus.isNewPost).toBe(true);
      expect(syncStatus.title).toBe(testTitle);
    });

    test("should show synced post state after successful sync", async () => {
      const testTitle = "Synced Post State Test";

      // Create and sync a post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await syncPost(context);

      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'synced');

      // Verify synced post UI elements are present
      const propertiesSection = await context.page.locator('.ghost-properties').count();
      expect(propertiesSection).toBe(1);

      const statusBadges = await context.page.locator('.ghost-status-badges').count();
      expect(statusBadges).toBe(1);
    });
  });

  describe("Ghost Tab Button Interactions", () => {
    test("should have working Sync button for new posts", async () => {
      const testTitle = "Sync Button Test";

      // Create a new post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'new-post');

      // Verify sync button is present and has correct text
      const syncButton = context.page.locator('.ghost-sync-btn').filter({
        hasText: 'Sync to Ghost'
      });
      await syncButton.waitFor({ timeout: 5000 });

      // Click sync button
      await clickSyncButton(context.page);
      await expectNotice(context, "Synced", 15000);

      // Verify state changed to synced
      await waitForGhostTabStatus(context.page, 'synced');
    });

    test("should have working Browse Posts button", async () => {
      await openGhostTab(context);

      // Click browse posts button
      await clickBrowsePostsButton(context.page);

      // Wait for the post browser modal to appear
      await context.page.waitForSelector('.ghost-post-suggestion', { timeout: 10000 });

      // Close the modal by pressing Escape
      await context.page.keyboard.press('Escape');
    });

    test("should have working Refresh button for synced posts", async () => {
      const testTitle = "Refresh Button Test";

      // Create and sync a post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await syncPost(context);
      await openGhostTab(context);

      // Click refresh button
      const refreshButton = context.page.locator('.ghost-sync-btn').filter({
        hasText: 'Refresh'
      });
      await refreshButton.waitFor({ timeout: 5000 });
      await refreshButton.click();

      await expectNotice(context, "Refreshing sync status...", 5000);
      await expectNotice(context, "Sync status refreshed", 10000);
    });
  });

  describe("Ghost Tab Status Displays", () => {
    test("should display status badges correctly", async () => {
      const testTitle = "Status Badges Test";

      // Create and sync a post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await syncPost(context);
      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'synced');

      // Verify status badges are present
      const statusBadges = await context.page.locator('.ghost-status-badges').count();
      expect(statusBadges).toBe(1);

      // Check for individual badge elements
      const statusBadge = await context.page.locator('.ghost-status-badge').first();
      await statusBadge.waitFor({ timeout: 5000 });

      const badgeText = await statusBadge.textContent();
      expect(badgeText).toContain('draft'); // Default status should be draft
    });

    test("should display property information correctly", async () => {
      const testTitle = "Property Display Test";

      // Create and sync a post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await syncPost(context);
      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'synced');

      // Verify property displays are present
      const propertiesSection = await context.page.locator('.ghost-properties').count();
      expect(propertiesSection).toBe(1);

      // Check for title property
      const titleProperty = context.page.locator('.ghost-property-section').filter({
        has: context.page.locator('.ghost-property-label', { hasText: 'Title' })
      });
      await titleProperty.waitFor({ timeout: 5000 });

      // Check for slug property
      const slugProperty = context.page.locator('.ghost-property-section').filter({
        has: context.page.locator('.ghost-property-label', { hasText: 'Slug' })
      });
      await slugProperty.waitFor({ timeout: 5000 });
    });

    test("should display sync metadata correctly", async () => {
      const testTitle = "Sync Metadata Test";

      // Create and sync a post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await syncPost(context);
      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'synced');

      // Verify sync metadata section is present
      const metadataSection = await context.page.locator('.ghost-sync-metadata-subtle').count();
      expect(metadataSection).toBe(1);

      // Check for Last Synced timestamp
      const lastSyncedLabel = context.page.locator('.ghost-sync-metadata-item').filter({
        has: context.page.locator('.label', { hasText: 'Last Synced:' })
      });
      await lastSyncedLabel.waitFor({ timeout: 5000 });

      // Check for Last Changed timestamp
      const lastChangedLabel = context.page.locator('.ghost-sync-metadata-item').filter({
        has: context.page.locator('.label', { hasText: 'Last Changed:' })
      });
      await lastChangedLabel.waitFor({ timeout: 5000 });
    });
  });

  describe("Ghost Tab Workflows", () => {
    test("should complete full workflow: create -> sync -> update -> sync", async () => {
      const testTitle = "Full Workflow Test";

      // Step 1: Create a new post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      // Step 2: Verify new post state in Ghost tab
      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'new-post');

      const initialSyncStatus = await getGhostTabSyncStatus(context.page);
      expect(initialSyncStatus.isNewPost).toBe(true);

      // Step 3: Sync to Ghost
      await clickSyncButton(context.page);
      await expectNotice(context, "Synced", 15000);

      // Step 4: Verify synced state
      await waitForGhostTabStatus(context.page, 'synced');

      // Step 5: Update the post content
      await context.page.evaluate(() => {
        const app = (window as any).app;
        const activeFile = app.workspace.getActiveFile();
        if (activeFile) {
          const currentContent = app.vault.read(activeFile);
          const updatedContent = currentContent.then((content: string) =>
            content + '\n\nThis is additional content added after sync.'
          );
          return updatedContent.then((newContent: string) =>
            app.vault.modify(activeFile, newContent)
          );
        }
      });

      // Wait a moment for the change to be detected
      await context.page.waitForTimeout(1000);

      // Step 6: Sync the updated content
      await clickSyncButton(context.page);
      await expectNotice(context, "Synced", 15000);

      // Verify the post is still in synced state
      await waitForGhostTabStatus(context.page, 'synced');
    });

    test("should handle publish workflow correctly", async () => {
      const testTitle = "Publish Workflow Test";

      // Create and sync a post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await syncPost(context);
      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'synced');

      // Verify publish button is present
      const publishButton = context.page.locator('.ghost-sync-btn').filter({
        hasText: 'Publish'
      });
      await publishButton.waitFor({ timeout: 5000 });

      // Note: We don't actually click publish in e2e tests as per the rules
      // Just verify the button is present and accessible
      const isEnabled = await publishButton.isEnabled();
      expect(isEnabled).toBe(true);
    });

    test("should handle delete workflow correctly", async () => {
      const testTitle = "Delete Workflow Test";

      // Create and sync a post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await syncPost(context);
      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'synced');

      // Verify delete button is present
      const deleteButton = context.page.locator('.ghost-delete-btn');
      await deleteButton.waitFor({ timeout: 5000 });

      // Verify delete button has the correct icon and is enabled
      const deleteButtonText = await deleteButton.textContent();
      expect(deleteButtonText).toContain('🗑️');
      expect(deleteButtonText).toContain('Delete');

      const isEnabled = await deleteButton.isEnabled();
      expect(isEnabled).toBe(true);
    });

    test("should show correct buttons for different post states", async () => {
      const testTitle = "Button States Test";

      // Test new post buttons
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle },
        'submit'
      );
      await expectNotice(context, "Created new post");

      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'new-post');

      // For new posts, should have "Sync to Ghost" and "Browse Posts" buttons
      const syncToGhostButton = context.page.locator('.ghost-sync-btn').filter({
        hasText: 'Sync to Ghost'
      });
      await syncToGhostButton.waitFor({ timeout: 5000 });

      const browsePostsButton = context.page.locator('.ghost-sync-btn').filter({
        hasText: 'Browse Posts'
      });
      await browsePostsButton.waitFor({ timeout: 5000 });

      // Should NOT have publish, delete, or refresh buttons for new posts
      const publishButtonCount = await context.page.locator('.ghost-sync-btn').filter({
        hasText: 'Publish'
      }).count();
      expect(publishButtonCount).toBe(0);

      const deleteButtonCount = await context.page.locator('.ghost-delete-btn').count();
      expect(deleteButtonCount).toBe(0);

      // Sync the post
      await clickSyncButton(context.page);
      await expectNotice(context, "Synced", 15000);
      await waitForGhostTabStatus(context.page, 'synced');

      // For synced posts, should have different buttons
      const syncButton = context.page.locator('.ghost-sync-btn').filter({
        hasText: /^Sync$/
      });
      await syncButton.waitFor({ timeout: 5000 });

      const refreshButton = context.page.locator('.ghost-sync-btn').filter({
        hasText: 'Refresh'
      });
      await refreshButton.waitFor({ timeout: 5000 });

      const publishButtonSynced = context.page.locator('.ghost-sync-btn').filter({
        hasText: 'Publish'
      });
      await publishButtonSynced.waitFor({ timeout: 5000 });

      const deleteButtonSynced = context.page.locator('.ghost-delete-btn');
      await deleteButtonSynced.waitFor({ timeout: 5000 });
    });
  });

  describe("Ghost Tab Accessibility and Usability", () => {
    test("should be accessible via command palette", async () => {
      // Test opening Ghost tab via command
      await executeCommand(context, 'Ghost Sync: Open Ghost Sync Status');

      // Verify the tab is visible
      const isVisible = await isGhostTabVisible(context.page);
      expect(isVisible).toBe(true);
    });

    test("should maintain state when switching between files", async () => {
      const testTitle1 = "File Switch Test 1";
      const testTitle2 = "File Switch Test 2";

      // Create first post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle1 },
        'submit'
      );
      await expectNotice(context, "Created new post");

      // Create second post
      await executeCommand(context, 'Ghost Sync: Create new post');
      await completeModalInteraction(
        context.page,
        'create-post',
        { 'post-title': testTitle2 },
        'submit'
      );
      await expectNotice(context, "Created new post");

      // Open Ghost tab and verify it shows the current file (second post)
      await openGhostTab(context);
      await waitForGhostTabStatus(context.page, 'new-post');

      const syncStatus = await getGhostTabSyncStatus(context.page);
      expect(syncStatus.title).toBe(testTitle2);

      // Switch to first file
      await context.page.evaluate((title) => {
        const app = (window as any).app;
        const file = app.vault.getAbstractFileByPath(`articles/${title.toLowerCase().replace(/\s+/g, '-')}.md`);
        if (file) {
          app.workspace.getLeaf().openFile(file);
        }
      }, testTitle1);

      // Wait for the Ghost tab to update
      await context.page.waitForTimeout(1000);

      // Verify Ghost tab now shows the first file
      const updatedSyncStatus = await getGhostTabSyncStatus(context.page);
      expect(updatedSyncStatus.title).toBe(testTitle1);
    });
  });
});
